import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Navbar } from './components/Navbar';
import { Hero } from './components/Hero';
import { About } from './components/About';
import { Skills } from './components/Skills';
import { Team } from './components/Team';
import { Contact } from './components/Contact';
import { Footer } from './components/Footer';
import { LoadingScreen } from './components/LoadingScreen';
import { AnimatedBackground } from './components/AnimatedBackground';
import { Pricing } from './components/Pricing';
import { AdminDashboard } from './components/admin/Dashboard';
import { Projects } from './components/Projects';
import { AdBanner } from './components/AdBanner';
import AdminLogin from './pages/AdminLogin';
import ProtectedRoute from './components/ProtectedRoute';

// Home page component
const HomePage: React.FC = () => {
  return (
    <div className="relative min-h-screen bg-black">
      <AnimatedBackground />
      <div className="relative z-10">
        <Navbar />
        <main>
          <Hero setShowAllWorks={() => {}} />
          <AdBanner position="hero" className="mx-4 md:mx-6 my-8" />
          <About />
          <Skills />
          <Projects />
          <Team />
          <Pricing />
          <Contact />
          <AdBanner position="footer" className="mx-4 md:mx-6 my-8" />
        </main>
        <AdBanner position="popup" />
        <Footer />
      </div>
    </div>
  );
};

export function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2500);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <LoadingScreen onComplete={() => setIsLoading(false)} />;
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/admin" element={<AdminLogin />} />
        <Route
          path="/admin/dashboard"
          element={
            <ProtectedRoute>
              <AdminDashboard />
            </ProtectedRoute>
          }
        />
      </Routes>
    </Router>
  );
}