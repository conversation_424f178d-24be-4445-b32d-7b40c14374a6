import React, { useEffect, useState } from 'react';
import { Navbar } from './components/Navbar';
import { Hero } from './components/Hero';
import { About } from './components/About';
import { Skills } from './components/Skills';
import { Team } from './components/Team';
import { Contact } from './components/Contact';
import { Footer } from './components/Footer';
import { LoadingScreen } from './components/LoadingScreen';
import { AnimatedBackground } from './components/AnimatedBackground';
import { Pricing } from './components/Pricing';
import { AdminDashboard } from './components/admin/Dashboard';
import { Projects } from './components/Projects';
import { AdBanner } from './components/AdBanner';

export function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2500);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <LoadingScreen onComplete={() => setIsLoading(false)} />;
  }

  // Add this condition to check for admin route
  if (window.location.pathname === '/admin') {
    return <AdminDashboard />;
  }

  return (
    <div className="relative min-h-screen bg-black">
      <AnimatedBackground />
      <div className="relative z-10">
        <Navbar />
        <main>
          <Hero setShowAllWorks={() => {}} />
          <AdBanner position="hero" className="mx-4 md:mx-6 my-8" />
          <About />
          <Skills />
          <Projects />
          <Team />
          <Pricing />
          <Contact />
          <AdBanner position="footer" className="mx-4 md:mx-6 my-8" />
        </main>
        <AdBanner position="popup" />
        <Footer />
      </div>
    </div>
  );
}