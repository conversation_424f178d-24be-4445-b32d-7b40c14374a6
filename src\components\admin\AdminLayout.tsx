import React, { useState } from 'react';
import { Menu, X, LogOut, Home } from 'lucide-react';
import { logout } from '../../hooks/useAuth';

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
}

interface AdminLayoutProps {
  children: React.ReactNode;
  menuItems: MenuItem[];
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function AdminLayout({ children, menuItems, activeTab, onTabChange }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleBackToSite = () => {
    window.location.href = '/';
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="bg-gray-900/50 backdrop-blur-sm border-b border-gray-800 px-3 md:px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 md:space-x-4 space-x-reverse">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-800 transition-colors"
            >
              {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
            </button>
            <h1 className="text-lg md:text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              لوحة التحكم - AR Team
            </h1>
          </div>

          <div className="flex items-center space-x-2 md:space-x-3 space-x-reverse">
            <button
              onClick={handleBackToSite}
              className="flex items-center space-x-1 md:space-x-2 space-x-reverse px-2 md:px-3 py-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors text-sm md:text-base"
            >
              <Home size={14} className="md:w-4 md:h-4" />
              <span className="hidden sm:inline">العودة للموقع</span>
              <span className="sm:hidden">الموقع</span>
            </button>
            <button
              onClick={handleLogout}
              className="flex items-center space-x-1 md:space-x-2 space-x-reverse px-2 md:px-3 py-2 rounded-lg bg-red-600 hover:bg-red-700 transition-colors text-sm md:text-base"
            >
              <LogOut size={14} className="md:w-4 md:h-4" />
              <span className="hidden sm:inline">تسجيل الخروج</span>
              <span className="sm:hidden">خروج</span>
            </button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`
          fixed lg:static inset-y-0 left-0 z-50 w-64 md:w-72 bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 transform transition-transform duration-300 ease-in-out
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}>
          <div className="p-3 md:p-4 pt-16 lg:pt-4">
            <nav className="space-y-1 md:space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      onTabChange(item.id);
                      setSidebarOpen(false);
                    }}
                    className={`
                      w-full flex items-center space-x-3 space-x-reverse px-3 md:px-4 py-2 md:py-3 rounded-lg transition-all duration-200 text-sm md:text-base
                      ${activeTab === item.id
                        ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/25'
                        : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                      }
                    `}
                  >
                    <Icon size={18} className="md:w-5 md:h-5" />
                    <span className="font-medium">{item.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </aside>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <main className="flex-1 p-3 md:p-6 lg:p-8 min-h-screen">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
