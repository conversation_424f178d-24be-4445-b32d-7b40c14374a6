import React from 'react';
import { MapPinIcon, MailIcon, PhoneIcon } from 'lucide-react';

export function Contact() {
  const contactInfo = [{
    icon: MapPinIcon,
    text: 'Samanoud, Al-Gharbia, Egypt'
  }, {
    icon: MailIcon,
    text: '<EMAIL>'
  }, {
    icon: PhoneIcon,
    text: '+201507647203'
  }];

  return <section id="contact" className="py-20 bg-black w-full">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white [text-shadow:_0_0_10px_rgb(168_85_247_/_0.5)]">
              Get In Touch
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-purple-500 via-fuchsia-500 to-pink-500 mx-auto mb-6 shadow-[0_0_15px_rgb(168_85_247_/_0.5)]"></div>
          </div>
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-8 border border-purple-500/20 transition-all duration-300 hover:border-purple-500/50 hover:shadow-[0_0_30px_rgba(168,85,247,0.3)]">
            <div className="space-y-6">
              {contactInfo.map((info, index) => <div key={index} className="flex items-center gap-4 text-gray-300">
                  <div className="p-2 bg-purple-500/10 rounded-lg">
                    <info.icon size={24} className="text-purple-400" />
                  </div>
                  <span>{info.text}</span>
                </div>)}
            </div>
          </div>
        </div>
      </div>
    </section>;
}