import React, { useState } from 'react';
import { AdminLayout } from './AdminLayout';
import { ProjectsManager } from './ProjectsManager';
import { TeamManager } from './TeamManager';
import { PricingManager } from './PricingManager';
import { AdsManager } from './AdsManager';
import { 
  LayoutDashboard, 
  FolderOpen, 
  Users, 
  DollarSign, 
  Megaphone,
  BarChart3,
  Settings
} from 'lucide-react';

type ActiveTab = 'overview' | 'projects' | 'team' | 'pricing' | 'ads' | 'analytics' | 'settings';

export function AdminDashboard() {
  const [activeTab, setActiveTab] = useState<ActiveTab>('overview');

  const menuItems = [
    { id: 'overview', label: 'نظرة عامة', icon: LayoutDashboard },
    { id: 'projects', label: 'المشاريع', icon: FolderOpen },
    { id: 'team', label: 'الفريق', icon: Users },
    { id: 'pricing', label: 'الأسعار', icon: DollarSign },
    { id: 'ads', label: 'الإعلانات', icon: Megaphone },
    { id: 'analytics', label: 'التحليلات', icon: BarChart3 },
    { id: 'settings', label: 'الإعدادات', icon: Settings },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview />;
      case 'projects':
        return <ProjectsManager />;
      case 'team':
        return <TeamManager />;
      case 'pricing':
        return <PricingManager />;
      case 'ads':
        return <AdsManager />;
      case 'analytics':
        return <AnalyticsView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <AdminLayout 
      menuItems={menuItems}
      activeTab={activeTab}
      onTabChange={setActiveTab}
    >
      {renderContent()}
    </AdminLayout>
  );
}

function DashboardOverview() {
  const stats = [
    { label: 'إجمالي المشاريع', value: '12', color: 'purple' },
    { label: 'أعضاء الفريق', value: '2', color: 'blue' },
    { label: 'الإعلانات النشطة', value: '5', color: 'green' },
    { label: 'الباقات المتاحة', value: '3', color: 'orange' },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">لوحة التحكم</h1>
        <p className="text-gray-400">مرحباً بك في لوحة تحكم الموقع</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{stat.label}</p>
                <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
              </div>
              <div className={`w-12 h-12 rounded-lg bg-${stat.color}-500/20 flex items-center justify-center`}>
                <div className={`w-6 h-6 bg-${stat.color}-500 rounded`}></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4">المشاريع الأخيرة</h3>
          <div className="space-y-3">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex items-center space-x-3 space-x-reverse">
                <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <FolderOpen size={20} className="text-purple-400" />
                </div>
                <div className="flex-1">
                  <p className="text-white font-medium">مشروع تصميم {item}</p>
                  <p className="text-gray-400 text-sm">منذ {item} أيام</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4">الإحصائيات السريعة</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">معدل إنجاز المشاريع</span>
              <span className="text-green-400 font-bold">85%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">رضا العملاء</span>
              <span className="text-blue-400 font-bold">95%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">المشاريع الجارية</span>
              <span className="text-orange-400 font-bold">3</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function AnalyticsView() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-white">التحليلات</h1>
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
        <p className="text-gray-400">قريباً... سيتم إضافة تحليلات مفصلة</p>
      </div>
    </div>
  );
}

function SettingsView() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-white">الإعدادات</h1>
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
        <p className="text-gray-400">قريباً... سيتم إضافة إعدادات النظام</p>
      </div>
    </div>
  );
}
