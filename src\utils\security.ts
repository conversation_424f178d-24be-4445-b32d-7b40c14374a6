// حماية إضافية ضد التلاعب
export const initSecurity = () => {
  // منع فتح Developer Tools
  let devtools = {
    open: false,
    orientation: null as string | null
  };

  const threshold = 160;

  setInterval(() => {
    if (window.outerHeight - window.innerHeight > threshold || 
        window.outerWidth - window.innerWidth > threshold) {
      if (!devtools.open) {
        devtools.open = true;
        console.clear();
        console.warn('تحذير: محاولة الوصول غير المصرح بها!');
        // يمكن إضافة إجراءات إضافية هنا
      }
    } else {
      devtools.open = false;
    }
  }, 500);

  // منع النقر بالزر الأيمن
  document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
  });

  // منع اختصارات لوحة المفاتيح للمطورين
  document.addEventListener('keydown', (e) => {
    // F12
    if (e.key === 'F12') {
      e.preventDefault();
    }
    // Ctrl+Shift+I
    if (e.ctrlKey && e.shiftKey && e.key === 'I') {
      e.preventDefault();
    }
    // Ctrl+Shift+J
    if (e.ctrlKey && e.shiftKey && e.key === 'J') {
      e.preventDefault();
    }
    // Ctrl+U
    if (e.ctrlKey && e.key === 'u') {
      e.preventDefault();
    }
    // Ctrl+Shift+C
    if (e.ctrlKey && e.shiftKey && e.key === 'C') {
      e.preventDefault();
    }
  });

  // حماية ضد تعديل localStorage من console
  const originalSetItem = localStorage.setItem;
  const originalRemoveItem = localStorage.removeItem;
  const originalClear = localStorage.clear;

  // تسجيل محاولات التعديل المشبوهة
  let suspiciousActivity = 0;

  localStorage.setItem = function(key: string, value: string) {
    // السماح فقط للمفاتيح المصرح بها
    const allowedKeys = ['adminAuth', 'adminUser', 'adminLoginTime'];
    
    if (allowedKeys.includes(key)) {
      return originalSetItem.call(this, key, value);
    } else {
      suspiciousActivity++;
      console.warn('محاولة تعديل غير مصرح بها:', key);
      
      if (suspiciousActivity > 3) {
        // مسح جميع بيانات المصادقة
        originalRemoveItem.call(this, 'adminAuth');
        originalRemoveItem.call(this, 'adminUser');
        originalRemoveItem.call(this, 'adminLoginTime');
        window.location.href = '/admin';
      }
    }
  };

  localStorage.removeItem = function(key: string) {
    return originalRemoveItem.call(this, key);
  };

  localStorage.clear = function() {
    return originalClear.call(this);
  };
};

// فحص تكامل البيانات
export const validateAuthData = (): boolean => {
  const adminAuth = localStorage.getItem('adminAuth');
  const adminUser = localStorage.getItem('adminUser');
  const loginTime = localStorage.getItem('adminLoginTime');

  if (!adminAuth || !adminUser || !loginTime) {
    return false;
  }

  // فحص صحة البيانات
  if (adminAuth !== 'true' || adminUser !== 'ARTEAM') {
    return false;
  }

  // فحص صحة التوقيت
  const timestamp = parseInt(loginTime);
  if (isNaN(timestamp) || timestamp <= 0) {
    return false;
  }

  // فحص انتهاء الصلاحية
  const currentTime = Date.now();
  const sessionDuration = 24 * 60 * 60 * 1000; // 24 ساعة

  if (currentTime - timestamp > sessionDuration) {
    return false;
  }

  return true;
};

// تشفير بسيط للبيانات الحساسة
export const encryptData = (data: string): string => {
  return btoa(data);
};

export const decryptData = (encryptedData: string): string => {
  try {
    return atob(encryptedData);
  } catch {
    return '';
  }
};
