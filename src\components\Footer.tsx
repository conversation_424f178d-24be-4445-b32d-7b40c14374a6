import React from 'react';
import { ArrowUpIcon, FacebookIcon } from 'lucide-react';
export function Footer() {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  return <footer className="bg-black text-white py-12 w-full relative">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-xl font-bold mb-4 [text-shadow:_0_0_10px_rgb(168_85_247_/_0.5)]">
              Egyptian Designers
            </h3>
            <p className="text-gray-400 mb-4">
              Creating beautiful digital experiences with modern technologies
              and creative solutions.
            </p>
          </div>
          <div className="grid grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                {['Home', 'About', 'Projects', 'Team', 'Pricing', 'Contact'].map(item => <li key={item}>
                    <a href={`#${item.toLowerCase()}`} className="text-gray-400 hover:text-purple-400 transition-colors">
                      {item}
                    </a>
                  </li>)}
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Contact Info</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Samanoud, Al-Gharbia, Egypt</li>
                <li><EMAIL></li>
                <li>+201507647203</li>
              </ul>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm mb-4 md:mb-0">
            © 2025  All rights reserved AR Team. 
          </p>
          <div className="flex items-center gap-4">
            <a href="https://www.facebook.com/dark50014" target="_blank" rel="noopener noreferrer" className="p-2 bg-gradient-to-r from-purple-500 to-fuchsia-500 rounded-full transition-all duration-300 hover:shadow-[0_0_15px_rgba(168,85,247,0.5)] hover:scale-110">
              <FacebookIcon size={20} className="text-white" />
            </a>
            <button onClick={scrollToTop} className="p-2 bg-gradient-to-r from-purple-500 to-fuchsia-500 rounded-full transition-all duration-300 hover:shadow-[0_0_15px_rgba(168,85,247,0.5)] hover:scale-110">
              <ArrowUpIcon size={20} className="text-white" />
            </button>
          </div>
        </div>
      </div>
    </footer>;
}