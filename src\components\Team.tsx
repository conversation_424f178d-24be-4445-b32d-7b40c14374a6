import React, { useState } from 'react';
import { StarIcon } from 'lucide-react';
import { TeamMemberModal } from './TeamMemberModal';

export function Team() {
  const [selectedMember, setSelectedMember] = useState(null);
  
  const team = [
    {
      name: '<PERSON>',
      role: 'Graphic Designer',
      image: 'https://i.postimg.cc/vH2qnxxb/profile.png',
      phone: '+20 ************',
      email: '<PERSON><PERSON><PERSON><PERSON>@gmail.com',
      location: 'Samanoud, Al-Gharbia, Egypt',
      facebook: 'https://www.facebook.com/profile.php?id=61576318977504',
      skills: {
        'Menu Design': 97,
        'Cards Design': 96,
        'Posters': 89
      }
    },
    // Removed empty object here
    {
      name: '<PERSON><PERSON>',
      role: 'Graphic Designer',
      image: 'https://i.postimg.cc/GpzGCgy3/rada.png',
      phone: '+20 ************',
      email: '<EMAIL>',
      location: 'Nesha, Nabro, Egypt',
      facebook: 'https://www.facebook.com/profile.php?id=61576318977504',
      skills: {
        'Menu Design': 96,
        'Cards Design': 90,
        'Posters': 88
      }
    }
  ];

  return (
    <section id="team" className="relative py-20 bg-gray-900 w-full overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-pink-900/20" />
        <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob" />
        <div className="absolute top-0 -right-4 w-72 h-72 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-2000" />
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-4000" />
      </div>
      
      <div className="container relative mx-auto px-4 md:px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              AR Team
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 mx-auto mb-6"></div>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Meet our talented team of creative professionals
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-[800px] mx-auto">
            {team.map((member, index) => (
              <div key={index} className="group relative bg-gray-800/50 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-gray-700/50 transition-all duration-300 hover:shadow-[0_0_30px_rgba(168,85,247,0.3)]">
                <div className="relative rounded-xl overflow-hidden">
                  <div className="relative aspect-square overflow-hidden">
                    <img 
                      src={member.image} 
                      alt={member.name} 
                      className="w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-110" 
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent opacity-100 transition-opacity duration-300 group-hover:opacity-0"></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-purple-600/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  
                  <div className="p-6 relative">
                    <h3 className="text-xl font-bold text-white mb-1 group-hover:text-purple-300 transition-colors [text-shadow:_0_0_10px_rgb(168_85_247_/_0.3)]">
                      {member.name}
                    </h3>
                    <p className="text-purple-400 mb-4">{member.role}</p>
                    
                    <div className="space-y-4">
                      {Object.entries(member.skills).map(([skill, level], idx) => (
                        <div key={idx}>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-300 group-hover:text-gray-200">
                              {skill}
                            </span>
                            <span className="text-sm text-purple-400 group-hover:text-purple-300">
                              {level}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-700/50 rounded-full h-1 overflow-hidden transition-all duration-300 group-hover:bg-gray-600/50">
                            <div 
                              className="h-full rounded-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:shadow-[0_0_10px_rgba(168,85,247,0.5)] group-hover:from-purple-400 group-hover:to-pink-400" 
                              style={{ width: `${level}%` }} 
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <button 
                      onClick={() => window.open(member.facebook, '_blank')}
                      className="mt-6 w-full px-4 py-2 bg-transparent border border-purple-500 text-purple-400 rounded-lg transition-all duration-300 hover:bg-purple-500/10 group-hover:border-purple-400 group-hover:text-purple-300 group-hover:shadow-[0_0_15px_rgba(168,85,247,0.2)]"
                    >
                      View Facebook Page
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {selectedMember && (
        <TeamMemberModal 
          member={selectedMember} 
          onClose={() => setSelectedMember(null)} 
        />
      )}
    </section>
  );
}