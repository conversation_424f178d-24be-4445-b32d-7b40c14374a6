import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface Advertisement {
  id: number;
  title: string;
  description: string;
  image: string;
  link: string;
  isActive: boolean;
  position: 'hero' | 'sidebar' | 'footer' | 'popup';
  startDate: string;
  endDate: string;
}

interface AdBannerProps {
  position: 'hero' | 'sidebar' | 'footer' | 'popup';
  className?: string;
}

export function AdBanner({ position, className = '' }: AdBannerProps) {
  const [ads, setAds] = useState<Advertisement[]>([]);
  const [showPopup, setShowPopup] = useState(false);
  const [currentAdIndex, setCurrentAdIndex] = useState(0);

  useEffect(() => {
    loadAds();
  }, []);

  useEffect(() => {
    if (position === 'popup' && ads.length > 0) {
      const timer = setTimeout(() => {
        setShowPopup(true);
      }, 3000); // Show popup after 3 seconds
      return () => clearTimeout(timer);
    }
  }, [ads, position]);

  const loadAds = () => {
    const savedAds = localStorage.getItem('advertisements');
    if (savedAds) {
      const allAds = JSON.parse(savedAds);
      const activeAds = allAds.filter((ad: Advertisement) => {
        if (!ad.isActive) return false;
        
        const now = new Date();
        const start = new Date(ad.startDate);
        const end = new Date(ad.endDate);
        
        return now >= start && now <= end && ad.position === position;
      });
      setAds(activeAds);
    }
  };

  const handleAdClick = (link: string) => {
    if (link.startsWith('#')) {
      const element = document.querySelector(link);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else if (link.startsWith('http')) {
      window.open(link, '_blank');
    }
  };

  const nextAd = () => {
    setCurrentAdIndex((prev) => (prev + 1) % ads.length);
  };

  const prevAd = () => {
    setCurrentAdIndex((prev) => (prev - 1 + ads.length) % ads.length);
  };

  if (ads.length === 0) return null;

  if (position === 'popup' && showPopup) {
    const ad = ads[currentAdIndex];
    return (
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900 rounded-lg max-w-md w-full overflow-hidden border border-purple-500/30">
          <div className="relative">
            <img
              src={ad.image}
              alt={ad.title}
              className="w-full h-48 object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x200?text=Ad+Image';
              }}
            />
            <button
              onClick={() => setShowPopup(false)}
              className="absolute top-2 right-2 p-1 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
            >
              <X size={16} />
            </button>
          </div>
          <div className="p-4">
            <h3 className="text-lg font-bold text-white mb-2">{ad.title}</h3>
            <p className="text-gray-400 mb-4">{ad.description}</p>
            <button
              onClick={() => {
                handleAdClick(ad.link);
                setShowPopup(false);
              }}
              className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              اعرف المزيد
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (position === 'hero') {
    const ad = ads[currentAdIndex];
    return (
      <div className={`relative bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-lg overflow-hidden border border-purple-500/30 ${className}`}>
        <div className="flex flex-col md:flex-row">
          <div className="md:w-1/2 p-6">
            <h3 className="text-xl font-bold text-white mb-2">{ad.title}</h3>
            <p className="text-gray-300 mb-4">{ad.description}</p>
            <button
              onClick={() => handleAdClick(ad.link)}
              className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              اعرف المزيد
            </button>
          </div>
          <div className="md:w-1/2">
            <img
              src={ad.image}
              alt={ad.title}
              className="w-full h-48 md:h-full object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x200?text=Ad+Image';
              }}
            />
          </div>
        </div>
        {ads.length > 1 && (
          <div className="absolute bottom-4 left-4 flex space-x-2 space-x-reverse">
            <button
              onClick={prevAd}
              className="p-1 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
            >
              ←
            </button>
            <button
              onClick={nextAd}
              className="p-1 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
            >
              →
            </button>
          </div>
        )}
      </div>
    );
  }

  if (position === 'sidebar') {
    return (
      <div className={`space-y-4 ${className}`}>
        {ads.map((ad) => (
          <div
            key={ad.id}
            className="bg-gray-800/50 rounded-lg overflow-hidden border border-gray-700 cursor-pointer hover:border-purple-500/50 transition-colors"
            onClick={() => handleAdClick(ad.link)}
          >
            <img
              src={ad.image}
              alt={ad.title}
              className="w-full h-32 object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x150?text=Ad+Image';
              }}
            />
            <div className="p-3">
              <h4 className="text-sm font-bold text-white mb-1">{ad.title}</h4>
              <p className="text-xs text-gray-400">{ad.description}</p>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (position === 'footer') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ${className}`}>
        {ads.map((ad) => (
          <div
            key={ad.id}
            className="bg-gray-800/50 rounded-lg overflow-hidden border border-gray-700 cursor-pointer hover:border-purple-500/50 transition-colors"
            onClick={() => handleAdClick(ad.link)}
          >
            <img
              src={ad.image}
              alt={ad.title}
              className="w-full h-24 object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x100?text=Ad+Image';
              }}
            />
            <div className="p-3">
              <h4 className="text-sm font-bold text-white mb-1">{ad.title}</h4>
              <p className="text-xs text-gray-400">{ad.description}</p>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return null;
}
