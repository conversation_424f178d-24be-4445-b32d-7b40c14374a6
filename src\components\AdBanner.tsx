import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface Advertisement {
  id: number;
  title: string;
  description: string;
  image: string;
  link: string;
  isActive: boolean;
  position: 'hero' | 'sidebar' | 'footer' | 'popup';
  startDate: string;
  endDate: string;
  displayDuration: number; // مدة العرض بالثواني
  autoHide: boolean; // إخفاء تلقائي بعد المدة المحددة
}

interface AdBannerProps {
  position: 'hero' | 'sidebar' | 'footer' | 'popup';
  className?: string;
}

export function AdBanner({ position, className = '' }: AdBannerProps) {
  const [ads, setAds] = useState<Advertisement[]>([]);
  const [showPopup, setShowPopup] = useState(false);
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const [hiddenAds, setHiddenAds] = useState<Set<number>>(new Set());
  const [adTimers, setAdTimers] = useState<Map<number, NodeJS.Timeout>>(new Map());
  const [imageAspectRatios, setImageAspectRatios] = useState<Map<number, number>>(new Map());

  useEffect(() => {
    loadAds();
    // Reload ads when localStorage changes
    const handleStorageChange = () => {
      loadAds();
    };
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [position, hiddenAds]);

  useEffect(() => {
    if (position === 'popup' && ads.length > 0) {
      const timer = setTimeout(() => {
        setShowPopup(true);
      }, 5000); // Show popup after 5 seconds
      return () => clearTimeout(timer);
    }
  }, [ads, position]);

  // إدارة مؤقتات الإعلانات للإخفاء التلقائي
  useEffect(() => {
    // مسح المؤقتات السابقة
    adTimers.forEach(timer => clearTimeout(timer));
    const newTimers = new Map<number, NodeJS.Timeout>();

    ads.forEach(ad => {
      if (ad.autoHide && ad.displayDuration > 0 && !hiddenAds.has(ad.id)) {
        const timer = setTimeout(() => {
          setHiddenAds(prev => new Set([...prev, ad.id]));
        }, ad.displayDuration * 1000);
        newTimers.set(ad.id, timer);
      }
    });

    setAdTimers(newTimers);

    return () => {
      newTimers.forEach(timer => clearTimeout(timer));
    };
  }, [ads, hiddenAds]);

  const loadAds = () => {
    const savedAds = localStorage.getItem('advertisements');
    if (savedAds) {
      const allAds = JSON.parse(savedAds);
      const activeAds = allAds.filter((ad: Advertisement) => {
        if (!ad.isActive) return false;
        if (hiddenAds.has(ad.id)) return false;

        const now = new Date();
        const start = new Date(ad.startDate);
        const end = new Date(ad.endDate);

        return now >= start && now <= end && ad.position === position;
      });
      setAds(activeAds);
    }
  };

  const handleAdClick = (link: string) => {
    if (link.startsWith('#')) {
      const element = document.querySelector(link);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else if (link.startsWith('http')) {
      window.open(link, '_blank');
    }
  };

  const nextAd = () => {
    setCurrentAdIndex((prev) => (prev + 1) % ads.length);
  };

  const prevAd = () => {
    setCurrentAdIndex((prev) => (prev - 1 + ads.length) % ads.length);
  };

  const hideAd = (adId: number) => {
    setHiddenAds(prev => new Set([...prev, adId]));
    // مسح المؤقت إذا كان موجوداً
    const timer = adTimers.get(adId);
    if (timer) {
      clearTimeout(timer);
      setAdTimers(prev => {
        const newTimers = new Map(prev);
        newTimers.delete(adId);
        return newTimers;
      });
    }
  };

  if (ads.length === 0) return null;

  if (position === 'popup' && showPopup) {
    const ad = ads[currentAdIndex];
    return (
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900 rounded-lg max-w-md w-full overflow-hidden border border-purple-500/30">
          <div className="relative">
            <div className="w-full min-h-[120px] max-h-[300px] bg-gray-900/50 flex items-center justify-center p-2">
              <img
                src={ad.image}
                alt={ad.title}
                className="max-w-full h-auto object-contain rounded transition-all duration-300"
                style={{ maxHeight: '280px' }}
                onLoad={(e) => {
                  const img = e.target as HTMLImageElement;
                  const container = img.parentElement;
                  if (container && img.naturalWidth && img.naturalHeight) {
                    const aspectRatio = img.naturalWidth / img.naturalHeight;
                    if (aspectRatio > 2.5) {
                      // صورة عريضة جداً
                      container.style.minHeight = '100px';
                      container.style.maxHeight = '200px';
                    } else if (aspectRatio < 0.7) {
                      // صورة طويلة
                      container.style.minHeight = '200px';
                      container.style.maxHeight = '350px';
                    }
                  }
                }}
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x200?text=Ad+Image';
                }}
              />
            </div>
            <button
              onClick={() => setShowPopup(false)}
              className="absolute top-2 right-2 p-1 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
            >
              <X size={16} />
            </button>
          </div>
          <div className="p-4">
            <h3 className="text-lg font-bold text-white mb-2">{ad.title}</h3>
            <p className="text-gray-400 mb-4">{ad.description}</p>
            <button
              onClick={() => {
                handleAdClick(ad.link);
                setShowPopup(false);
              }}
              className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              اعرف المزيد
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (position === 'hero') {
    const ad = ads[currentAdIndex];
    return (
      <div className={`relative bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-lg overflow-hidden border border-purple-500/50 shadow-lg ${className}`}>
        <div className="flex flex-col md:flex-row min-h-[180px]">
          <div className="md:w-1/2 p-4 md:p-6 flex flex-col justify-center">
            <h3 className="text-lg md:text-xl font-bold text-white mb-2 md:mb-3">{ad.title}</h3>
            <p className="text-gray-300 mb-4 text-sm md:text-base">{ad.description}</p>
            <button
              onClick={() => handleAdClick(ad.link)}
              className="px-4 md:px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium w-fit"
            >
              اعرف المزيد
            </button>
          </div>
          <div className="md:w-1/2 min-h-[160px] max-h-[300px] bg-gray-900/30 flex items-center justify-center p-2 md:p-4">
            <img
              src={ad.image}
              alt={ad.title}
              className="max-w-full h-auto object-contain rounded transition-all duration-300"
              style={{ maxHeight: '280px' }}
              onLoad={(e) => {
                const img = e.target as HTMLImageElement;
                const container = img.parentElement;
                if (container && img.naturalWidth && img.naturalHeight) {
                  const aspectRatio = img.naturalWidth / img.naturalHeight;
                  if (aspectRatio > 2.5) {
                    // صورة عريضة جداً
                    container.style.minHeight = '140px';
                    container.style.maxHeight = '220px';
                  } else if (aspectRatio < 0.7) {
                    // صورة طويلة
                    container.style.minHeight = '200px';
                    container.style.maxHeight = '350px';
                  }
                }
              }}
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x200?text=Ad+Image';
              }}
            />
          </div>
        </div>
        {ads.length > 1 && (
          <div className="absolute bottom-2 md:bottom-4 left-2 md:left-4 flex space-x-2 space-x-reverse">
            <button
              onClick={prevAd}
              className="p-2 bg-black/60 rounded-full text-white hover:bg-black/80 transition-colors"
            >
              ←
            </button>
            <button
              onClick={nextAd}
              className="p-2 bg-black/60 rounded-full text-white hover:bg-black/80 transition-colors"
            >
              →
            </button>
          </div>
        )}
        <div className="absolute top-2 right-2 flex items-center space-x-2 space-x-reverse">
          <span className="bg-purple-600/80 text-white text-xs px-2 py-1 rounded">
            إعلان
          </span>
          <button
            onClick={() => hideAd(ad.id)}
            className="p-1 bg-black/60 rounded-full text-white hover:bg-black/80 transition-colors"
          >
            <X size={14} />
          </button>
        </div>
      </div>
    );
  }

  if (position === 'sidebar') {
    return (
      <div className={`space-y-3 md:space-y-4 ${className}`}>
        {ads.map((ad) => (
          <div
            key={ad.id}
            className="bg-gray-800/50 rounded-lg overflow-hidden border border-gray-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105 relative group"
          >
            <div className="relative cursor-pointer min-h-[100px] max-h-[200px] bg-gray-900/50 flex items-center justify-center p-2" onClick={() => handleAdClick(ad.link)}>
              <img
                src={ad.image}
                alt={ad.title}
                className="max-w-full h-auto object-contain rounded transition-all duration-300"
                style={{ maxHeight: '180px' }}
                onLoad={(e) => {
                  const img = e.target as HTMLImageElement;
                  const container = img.parentElement;
                  if (container && img.naturalWidth && img.naturalHeight) {
                    const aspectRatio = img.naturalWidth / img.naturalHeight;
                    if (aspectRatio > 2.5) {
                      // صورة عريضة جداً
                      container.style.minHeight = '80px';
                      container.style.maxHeight = '140px';
                    } else if (aspectRatio < 0.7) {
                      // صورة طويلة
                      container.style.minHeight = '140px';
                      container.style.maxHeight = '220px';
                    }
                  }
                }}
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x150?text=Ad+Image';
                }}
              />
              <div className="absolute top-1 right-1 flex items-center space-x-1 space-x-reverse">
                <span className="bg-purple-600/80 text-white text-xs px-1 py-0.5 rounded">
                  إعلان
                </span>
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                hideAd(ad.id);
              }}
              className="absolute top-1 left-1 p-1 bg-black/60 rounded-full text-white hover:bg-black/80 transition-colors opacity-0 group-hover:opacity-100"
            >
              <X size={12} />
            </button>
            <div className="p-3 cursor-pointer" onClick={() => handleAdClick(ad.link)}>
              <h4 className="text-sm font-bold text-white mb-1 line-clamp-1">{ad.title}</h4>
              <p className="text-xs text-gray-400 line-clamp-2">{ad.description}</p>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (position === 'footer') {
    return (
      <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 ${className}`}>
        {ads.map((ad) => (
          <div
            key={ad.id}
            className="bg-gray-800/50 rounded-lg overflow-hidden border border-gray-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105 relative group"
          >
            <div className="relative cursor-pointer min-h-[80px] max-h-[150px] bg-gray-900/50 flex items-center justify-center p-1 md:p-2" onClick={() => handleAdClick(ad.link)}>
              <img
                src={ad.image}
                alt={ad.title}
                className="max-w-full h-auto object-contain rounded transition-all duration-300"
                style={{ maxHeight: '130px' }}
                onLoad={(e) => {
                  const img = e.target as HTMLImageElement;
                  const container = img.parentElement;
                  if (container && img.naturalWidth && img.naturalHeight) {
                    const aspectRatio = img.naturalWidth / img.naturalHeight;
                    if (aspectRatio > 2.5) {
                      // صورة عريضة جداً
                      container.style.minHeight = '60px';
                      container.style.maxHeight = '100px';
                    } else if (aspectRatio < 0.7) {
                      // صورة طويلة
                      container.style.minHeight = '120px';
                      container.style.maxHeight = '180px';
                    }
                  }
                }}
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x100?text=Ad+Image';
                }}
              />
              <div className="absolute top-1 right-1 flex items-center space-x-1 space-x-reverse">
                <span className="bg-purple-600/80 text-white text-xs px-1 py-0.5 rounded">
                  إعلان
                </span>
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                hideAd(ad.id);
              }}
              className="absolute top-1 left-1 p-1 bg-black/60 rounded-full text-white hover:bg-black/80 transition-colors opacity-0 group-hover:opacity-100"
            >
              <X size={12} />
            </button>
            <div className="p-2 md:p-3 cursor-pointer" onClick={() => handleAdClick(ad.link)}>
              <h4 className="text-xs md:text-sm font-bold text-white mb-1 line-clamp-1">{ad.title}</h4>
              <p className="text-xs text-gray-400 line-clamp-2">{ad.description}</p>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return null;
}
