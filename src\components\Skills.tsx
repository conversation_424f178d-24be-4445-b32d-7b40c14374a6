import React, { useEffect, useState, useRef } from 'react';
import { Paintbrush2Icon, LayersIcon, VideoIcon, ShapesIcon, MonitorIcon, PenToolIcon } from 'lucide-react';
export function Skills() {
  const [isVisible, setIsVisible] = useState(false);
  const skillsRef = useRef(null);
  useEffect(() => {
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        setIsVisible(entry.isIntersecting);
      });
    }, {
      threshold: 0.1,
      rootMargin: '-50px'
    });
    if (skillsRef.current) {
      observer.observe(skillsRef.current);
    }
    return () => {
      if (skillsRef.current) {
        observer.unobserve(skillsRef.current);
      }
    };
  }, []);
  const skills = [{
    icon: Paintbrush2Icon,
    name: 'Adobe Photoshop',
    description: 'photo editing and digital art creation',
    level: 95
  }, {
    icon: LayersIcon,
    name: 'Adobe Illustrator',
    description: 'Vector graphics and logo design',
    level: 90
  }, {
    icon: VideoIcon,
    name: 'Adobe Premiere Pro',
    description: 'Video editing and post-production',
    level: 88
  }, {
    icon: ShapesIcon,
    name: 'Canva',
    description: 'Easy-to-use design for social media',
    level: 92
  }, {
    icon: MonitorIcon,
    name: 'Adobe After Effects',
    description: 'Motion graphics and visual effects',
    level: 85
  }, {
    icon: PenToolIcon,
    name: 'Adobe InDesign',
    description: 'Print and digital publication design',
    level: 87
  }];
  return <section id="skills" className="py-20 bg-black w-full relative overflow-hidden" ref={skillsRef}>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-purple-900/20 via-black to-black"></div>
      <div className="container relative mx-auto px-4 md:px-6 z-10">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white [text-shadow:_0_0_10px_rgb(168_85_247_/_0.5)]">
              Adobe Creative Suite
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-purple-500 via-fuchsia-500 to-pink-500 mx-auto mb-6 shadow-[0_0_15px_rgb(168_85_247_/_0.5)]"></div>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {skills.map((skill, index) => <div key={index} className="group relative bg-black/50 backdrop-blur-sm rounded-lg p-8 border border-purple-500/20 transition-all duration-300 hover:border-purple-500/50 hover:shadow-[0_0_30px_rgba(168,85,247,0.3)]" style={{
            transitionDelay: `${index * 100}ms`
          }}>
                <div className="relative">
                  <div className="mb-4 text-purple-400 group-hover:text-purple-300 transition-colors">
                    <skill.icon size={28} className="[filter:_drop-shadow(0_0_8px_rgb(168_85_247_/_0.5))]" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors [text-shadow:_0_0_10px_rgb(168_85_247_/_0.3)]">
                    {skill.name}
                  </h3>
                  <p className="text-gray-400 text-base mb-6 group-hover:text-gray-300 transition-colors relative group-hover:[text-shadow:_0_0_10px_rgb(168_85_247_/_0.2)]">
                    <span className="relative">
                      {skill.description}
                      <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-purple-500/50 transform scale-x-0 transition-transform origin-left group-hover:scale-x-100"></span>
                    </span>
                  </p>
                  <div className="relative">
                    <div className="w-full bg-gray-800/50 rounded-full h-4 overflow-hidden transition-all duration-300 group-hover:bg-gray-700/50 group-hover:shadow-[0_0_15px_rgba(168,85,247,0.2)]">
                      <div className="h-full rounded-full bg-gradient-to-r from-purple-500 via-fuchsia-500 to-pink-500 relative transition-all duration-1000 ease-out group-hover:shadow-[0_0_20px_rgba(168,85,247,0.5)]" style={{
                    width: isVisible ? `${skill.level}%` : '0%',
                    transition: 'width 1s ease-out'
                  }}>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:animate-shine"></div>
                      </div>
                    </div>
                    <span className="absolute -top-6 right-0 text-lg font-semibold text-purple-400 group-hover:text-purple-300 transition-all duration-1000 [text-shadow:_0_0_10px_rgb(168_85_247_/_0.3)]" style={{
                  opacity: isVisible ? 1 : 0,
                  transform: isVisible ? 'translateY(0)' : 'translateY(10px)'
                }}>
                      {isVisible ? `${skill.level}%` : '0%'}
                    </span>
                  </div>
                </div>
              </div>)}
          </div>
        </div>
      </div>
    </section>;
}