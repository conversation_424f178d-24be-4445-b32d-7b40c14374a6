import React from 'react';
import { XIcon, PhoneIcon, MailIcon, FacebookIcon } from 'lucide-react';

interface TeamMember {
  name: string;
  role: string;
  image: string;
  phone?: string;
  email?: string;
  facebook?: string;
  skills: Record<string, number>;
}

interface TeamMemberModalProps {
  member: TeamMember;
  onClose: () => void;
}

export const TeamMemberModal: React.FC<TeamMemberModalProps> = ({ member, onClose }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm">
      <div className="relative w-full max-w-4xl bg-gray-900 rounded-lg shadow-[0_0_30px_rgba(168,85,247,0.3)] overflow-hidden">
        <button
          onClick={onClose}
          aria-label="Close modal"
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white bg-gray-800/50 rounded-full transition-all duration-300 hover:bg-purple-500/20 group z-10 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <XIcon size={24} className="group-hover:scale-110 transition-transform duration-300" />
        </button>
        <div className="grid md:grid-cols-2">
          <div className="relative aspect-square">
            <img
              src={member.image}
              alt={`Portrait of ${member.name}`}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-transparent"></div>
          </div>
          <div className="p-8 relative">
            <h3 className="text-2xl font-bold text-white mb-2">{member.name}</h3>
            <p className="text-purple-400 mb-6">{member.role}</p>
            <div className="space-y-6">
              {member.phone && (
                <div className="flex items-center gap-4 text-gray-300">
                  <div className="p-2 bg-purple-500/10 rounded-lg">
                    <PhoneIcon size={20} className="text-purple-400" />
                  </div>
                  <span>{member.phone}</span>
                </div>
              )}
              {member.email && (
                <div className="flex items-center gap-4 text-gray-300">
                  <div className="p-2 bg-purple-500/10 rounded-lg">
                    <MailIcon size={20} className="text-purple-400" />
                  </div>
                  <span>{member.email}</span>
                </div>
              )}
            </div>
            <div className="mt-8">
              <h4 className="text-lg font-semibold text-white mb-4">Skills & Expertise</h4>
              <div className="space-y-4">
                {Object.entries(member.skills).map(([skill, level]) => (
                  <div key={skill}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-300">{skill}</span>
                      <span className="text-sm text-purple-400">{level}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-1.5 overflow-hidden">
                      <div
                        className={`h-full rounded-full bg-gradient-to-r from-purple-500 to-pink-500 w-[${level}%]`}
                        style={{ width: `${level}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {member.facebook && (
              <a
                href={member.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 mt-8 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-lg transition-colors"
              >
                <FacebookIcon size={20} />
                View Facebook Profile
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(TeamMemberModal);