import { useState, useEffect } from 'react';
import { validateAuthData } from '../utils/security';

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: string | null;
}

export const useAuth = (): AuthState => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
  });

  useEffect(() => {
    const checkAuth = () => {
      // استخدام دالة التحقق المحسنة
      const isValid = validateAuthData();

      if (!isValid) {
        // مسح البيانات غير الصالحة
        localStorage.removeItem('adminAuth');
        localStorage.removeItem('adminUser');
        localStorage.removeItem('adminLoginTime');

        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          user: null,
        });
        return;
      }

      // الجلسة صالحة
      const adminUser = localStorage.getItem('adminUser');
      setAuthState({
        isAuthenticated: true,
        isLoading: false,
        user: adminUser,
      });
    };

    checkAuth();

    // فحص دوري كل دقيقة
    const interval = setInterval(checkAuth, 60000);

    // فحص عند تغيير localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'adminAuth' || e.key === 'adminUser' || e.key === 'adminLoginTime') {
        checkAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return authState;
};

// دالة مساعدة لتسجيل الدخول
export const login = (username: string, password: string): boolean => {
  if (username === 'ARTEAM' && password === 'ARTEAM50014') {
    localStorage.setItem('adminAuth', 'true');
    localStorage.setItem('adminUser', username);
    localStorage.setItem('adminLoginTime', Date.now().toString());
    return true;
  }
  return false;
};

// دالة مساعدة لتسجيل الخروج
export const logout = (): void => {
  localStorage.removeItem('adminAuth');
  localStorage.removeItem('adminUser');
  localStorage.removeItem('adminLoginTime');
  localStorage.removeItem('adminSession');
  
  // إعادة تحميل الصفحة لضمان مسح جميع البيانات
  window.location.href = '/admin';
};
