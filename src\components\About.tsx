import React from 'react';
import { UserIcon, BriefcaseIcon, GraduationCapIcon, AwardIcon } from 'lucide-react';
export function About() {
  return <section id="about" className="relative py-20 bg-gray-900 w-full overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-pink-900/20" />
        <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob" />
        <div className="absolute top-0 -right-4 w-72 h-72 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-2000" />
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-4000" />
      </div>
      <div className="container relative mx-auto px-4 md:px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              About Me
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 mx-auto mb-6"></div>
          </div>
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="relative group">
              <div className="relative w-full aspect-square">
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 animate-spin-slow blur-md opacity-75"></div>
                <div className="absolute inset-1 bg-gray-900 rounded-full overflow-hidden">
                  <div className="absolute inset-2 overflow-hidden rounded-full">
                    <img src="https://i.postimg.cc/7YMgQsX6/Whats-App-Image-2025-05-13-at-21.jpg" />
                  </div>
                </div>
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 group-hover:opacity-75 transition-opacity duration-300"></div>
              </div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-purple-500 rounded-full opacity-20 blur-lg animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-pink-500 rounded-full opacity-20 blur-lg animate-pulse animation-delay-700"></div>
            </div>
            <div className="relative">
              <h3 className="text-2xl font-bold mb-4 text-white bg-clip-text">
                Who am I?
              </h3>
              <p className="text-gray-300 mb-6">
                I'm a passionate web developer and designer with over 1 years of
                experience creating digital solutions for clients worldwide. I
                specialize in building responsive websites, web applications,
                and interactive user interfaces.
              </p>
              <div className="grid grid-cols-2 gap-6 mb-8">
                {[{
                icon: UserIcon,
                title: 'Name',
                value: 'AR Designers'
              }, {
                icon: BriefcaseIcon,
                title: 'Experience',
                value: '1 Year'
              }, {
                icon: GraduationCapIcon,
                title: 'Education',
                value: "EL-Masrya (ASA)"
              }].map((item, index) => <div key={index} className="group relative bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 transition-all duration-300 hover:shadow-[0_0_25px_rgba(99,102,241,0.3)]">
                    <div className="flex items-start">
                      <div className="p-2 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-lg mr-3 group-hover:from-purple-500/20 group-hover:to-pink-500/20 transition-all duration-300">
                        <item.icon size={20} className="text-purple-400 group-hover:text-pink-400 transition-colors" />
                      </div>
                      <div>
                        <h4 className="font-semibold mb-1 text-gray-300">
                          {item.title}
                        </h4>
                        <p className="text-purple-400 group-hover:text-pink-400 transition-colors">
                          {item.value}
                        </p>
                      </div>
                    </div>
                  </div>)}
              </div>
              <a href="https://www.facebook.com/dark50014" target="_blank" rel="noopener noreferrer" className="inline-block px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-lg transition-all duration-300 hover:shadow-[0_0_20px_rgba(236,72,153,0.5)] transform hover:scale-105">
                Facebook Profile
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>;
}