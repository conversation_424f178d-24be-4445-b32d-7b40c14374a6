import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, User, Save, X } from 'lucide-react';

interface TeamMember {
  id: number;
  name: string;
  role: string;
  image: string;
  phone: string;
  email: string;
  location: string;
  facebook: string;
  skills: Record<string, number>;
}

export function TeamManager() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);

  useEffect(() => {
    loadTeamMembers();
  }, []);

  const loadTeamMembers = () => {
    const savedMembers = localStorage.getItem('teamMembers');
    if (savedMembers) {
      setTeamMembers(JSON.parse(savedMembers));
    } else {
      // تحميل البيانات الافتراضية من مكون Team
      const defaultMembers = [
        {
          id: 1,
          name: '<PERSON>',
          role: 'Graphic Designer',
          image: 'https://i.postimg.cc/vH2qnxxb/profile.png',
          phone: '+20 ************',
          email: '<PERSON><PERSON><EMAIL>',
          location: 'Samanoud, Al-Gharbia, Egypt',
          facebook: 'https://www.facebook.com/profile.php?id=61576318977504',
          skills: {
            'Menu Design': 97,
            'Cards Design': 96,
            'Posters': 89
          }
        },
        {
          id: 2,
          name: 'Reda Gaber',
          role: 'Graphic Designer',
          image: 'https://i.postimg.cc/GpzGCgy3/rada.png',
          phone: '+20 ************',
          email: '<EMAIL>',
          location: 'Nesha, Nabro, Egypt',
          facebook: 'https://www.facebook.com/profile.php?id=61576318977504',
          skills: {
            'Menu Design': 96,
            'Cards Design': 90,
            'Posters': 88
          }
        }
      ];
      setTeamMembers(defaultMembers);
      localStorage.setItem('teamMembers', JSON.stringify(defaultMembers));
    }
  };

  const saveTeamMembers = (updatedMembers: TeamMember[]) => {
    localStorage.setItem('teamMembers', JSON.stringify(updatedMembers));
    setTeamMembers(updatedMembers);
  };

  const handleAddMember = () => {
    setEditingMember(null);
    setShowForm(true);
  };

  const handleEditMember = (member: TeamMember) => {
    setEditingMember(member);
    setShowForm(true);
  };

  const handleDeleteMember = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا العضو؟')) {
      const updatedMembers = teamMembers.filter(m => m.id !== id);
      saveTeamMembers(updatedMembers);
    }
  };

  const handleSaveMember = (memberData: Omit<TeamMember, 'id'>) => {
    if (editingMember) {
      const updatedMembers = teamMembers.map(m => 
        m.id === editingMember.id 
          ? { ...memberData, id: editingMember.id }
          : m
      );
      saveTeamMembers(updatedMembers);
    } else {
      const newMember = {
        ...memberData,
        id: Date.now()
      };
      saveTeamMembers([...teamMembers, newMember]);
    }
    setShowForm(false);
    setEditingMember(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-white">إدارة الفريق</h1>
        <button
          onClick={handleAddMember}
          className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
        >
          <Plus size={20} />
          <span>إضافة عضو</span>
        </button>
      </div>

      {showForm && (
        <TeamMemberForm
          member={editingMember}
          onSave={handleSaveMember}
          onCancel={() => {
            setShowForm(false);
            setEditingMember(null);
          }}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {teamMembers.map((member) => (
          <div key={member.id} className="bg-gray-800/50 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-700">
            <div className="relative h-48">
              <img
                src={member.image}
                alt={member.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x300?text=No+Image';
                }}
              />
            </div>
            <div className="p-4">
              <h3 className="text-lg font-bold text-white mb-1">{member.name}</h3>
              <p className="text-purple-400 mb-2">{member.role}</p>
              <p className="text-gray-400 text-sm mb-2">{member.email}</p>
              <p className="text-gray-400 text-sm mb-4">{member.phone}</p>
              
              <div className="space-y-2 mb-4">
                {Object.entries(member.skills).map(([skill, level]) => (
                  <div key={skill} className="flex justify-between text-sm">
                    <span className="text-gray-300">{skill}</span>
                    <span className="text-purple-400">{level}%</span>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-end space-x-2 space-x-reverse">
                <button
                  onClick={() => handleEditMember(member)}
                  className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => handleDeleteMember(member.id)}
                  className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {teamMembers.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <User size={48} className="mx-auto mb-4" />
            <p>لا يوجد أعضاء في الفريق حالياً</p>
          </div>
        </div>
      )}
    </div>
  );
}

interface TeamMemberFormProps {
  member: TeamMember | null;
  onSave: (member: Omit<TeamMember, 'id'>) => void;
  onCancel: () => void;
}

function TeamMemberForm({ member, onSave, onCancel }: TeamMemberFormProps) {
  const [formData, setFormData] = useState({
    name: member?.name || '',
    role: member?.role || '',
    image: member?.image || '',
    phone: member?.phone || '',
    email: member?.email || '',
    location: member?.location || '',
    facebook: member?.facebook || '',
    skills: member?.skills || {}
  });

  const [newSkill, setNewSkill] = useState({ name: '', level: 0 });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name && formData.role && formData.email) {
      onSave(formData);
    }
  };

  const addSkill = () => {
    if (newSkill.name && newSkill.level > 0) {
      setFormData({
        ...formData,
        skills: { ...formData.skills, [newSkill.name]: newSkill.level }
      });
      setNewSkill({ name: '', level: 0 });
    }
  };

  const removeSkill = (skillName: string) => {
    const updatedSkills = { ...formData.skills };
    delete updatedSkills[skillName];
    setFormData({ ...formData, skills: updatedSkills });
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white">
          {member ? 'تعديل العضو' : 'إضافة عضو جديد'}
        </h2>
        <button
          onClick={onCancel}
          className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-white font-medium mb-2">الاسم</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
              required
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">المنصب</label>
            <input
              type="text"
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
              required
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">البريد الإلكتروني</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
              required
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">رقم الهاتف</label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">الموقع</label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">رابط فيسبوك</label>
            <input
              type="url"
              value={formData.facebook}
              onChange={(e) => setFormData({ ...formData, facebook: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-white font-medium mb-2">رابط الصورة</label>
          <input
            type="url"
            value={formData.image}
            onChange={(e) => setFormData({ ...formData, image: e.target.value })}
            className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
          />
        </div>

        <div>
          <label className="block text-white font-medium mb-2">المهارات</label>
          <div className="space-y-2 mb-4">
            {Object.entries(formData.skills).map(([skill, level]) => (
              <div key={skill} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                <span className="text-white">{skill}: {level}%</span>
                <button
                  type="button"
                  onClick={() => removeSkill(skill)}
                  className="text-red-400 hover:text-red-300"
                >
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
          
          <div className="flex space-x-2 space-x-reverse">
            <input
              type="text"
              placeholder="اسم المهارة"
              value={newSkill.name}
              onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
              className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
            <input
              type="number"
              placeholder="النسبة"
              min="0"
              max="100"
              value={newSkill.level || ''}
              onChange={(e) => setNewSkill({ ...newSkill, level: parseInt(e.target.value) || 0 })}
              className="w-20 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
            <button
              type="button"
              onClick={addSkill}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
            >
              إضافة
            </button>
          </div>
        </div>

        <div className="flex justify-end space-x-3 space-x-reverse">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
          >
            إلغاء
          </button>
          <button
            type="submit"
            className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
          >
            <Save size={16} />
            <span>حفظ</span>
          </button>
        </div>
      </form>
    </div>
  );
}
