import React, { useEffect, useState } from "react";
export function LoadingScreen({
  onComplete
}: {
  onComplete: () => void;
}) {
  const [progress, setProgress] = useState(0);
  useEffect(() => {
    const timer = setTimeout(() => {
      if (progress < 100) {
        setProgress(prev => {
          const next = prev + Math.random() * 15;
          return next >= 100 ? 100 : next;
        });
      } else {
        setTimeout(onComplete, 500);
      }
    }, 200);
    return () => clearTimeout(timer);
  }, [progress, onComplete]);
  return <div className="fixed inset-0 bg-gray-900 z-50 flex flex-col items-center justify-center">
      <div className="relative w-64 h-64">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full h-full border-4 border-purple-500/30 rounded-full animate-ping"></div>
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-3/4 h-3/4 border-4 border-cyan-500/30 rounded-full animate-ping animation-delay-300"></div>
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-1/2 h-1/2 border-4 border-pink-500/30 rounded-full animate-ping animation-delay-700"></div>
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <span className="text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-cyan-400 to-pink-400 [text-shadow:_0_0_15px_rgb(168_85_247_/_0.4)]">
              {Math.round(progress)}%
            </span>
          </div>
        </div>
      </div>
      <div className="mt-8 relative w-64 h-2 bg-gray-800 rounded-full overflow-hidden shadow-[0_0_15px_rgba(168,85,247,0.2)]">
        <div className="absolute h-full bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 transition-all duration-300" style={{
        width: `${progress}%`
      }}>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shine"></div>
        </div>
      </div>
    </div>;
}