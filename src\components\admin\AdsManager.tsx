import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Megaphone, Save, X, Eye, EyeOff } from 'lucide-react';

interface Advertisement {
  id: number;
  title: string;
  description: string;
  image: string;
  link: string;
  isActive: boolean;
  position: 'hero' | 'sidebar' | 'footer' | 'popup';
  startDate: string;
  endDate: string;
}

export function AdsManager() {
  const [advertisements, setAdvertisements] = useState<Advertisement[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingAd, setEditingAd] = useState<Advertisement | null>(null);

  useEffect(() => {
    loadAdvertisements();
  }, []);

  const loadAdvertisements = () => {
    const savedAds = localStorage.getItem('advertisements');
    if (savedAds) {
      setAdvertisements(JSON.parse(savedAds));
    } else {
      // بيانات افتراضية للإعلانات
      const defaultAds = [
        {
          id: 1,
          title: 'خصم خاص على تصميم القوائم',
          description: 'احصل على خصم 20% على جميع تصاميم القوائم لفترة محدودة',
          image: 'https://via.placeholder.com/400x200?text=Menu+Design+Offer',
          link: '#pricing',
          isActive: true,
          position: 'hero' as const,
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        {
          id: 2,
          title: 'عروض تصميم البوسترات',
          description: 'تصاميم احترافية للبوسترات بأسعار مميزة',
          image: 'https://via.placeholder.com/300x150?text=Poster+Design',
          link: '#contact',
          isActive: false,
          position: 'sidebar' as const,
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        }
      ];
      setAdvertisements(defaultAds);
      localStorage.setItem('advertisements', JSON.stringify(defaultAds));
    }
  };

  const saveAdvertisements = (updatedAds: Advertisement[]) => {
    localStorage.setItem('advertisements', JSON.stringify(updatedAds));
    setAdvertisements(updatedAds);
  };

  const handleAddAd = () => {
    setEditingAd(null);
    setShowForm(true);
  };

  const handleEditAd = (ad: Advertisement) => {
    setEditingAd(ad);
    setShowForm(true);
  };

  const handleDeleteAd = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا الإعلان؟')) {
      const updatedAds = advertisements.filter(ad => ad.id !== id);
      saveAdvertisements(updatedAds);
    }
  };

  const handleToggleActive = (id: number) => {
    const updatedAds = advertisements.map(ad => 
      ad.id === id ? { ...ad, isActive: !ad.isActive } : ad
    );
    saveAdvertisements(updatedAds);
  };

  const handleSaveAd = (adData: Omit<Advertisement, 'id'>) => {
    if (editingAd) {
      const updatedAds = advertisements.map(ad => 
        ad.id === editingAd.id 
          ? { ...adData, id: editingAd.id }
          : ad
      );
      saveAdvertisements(updatedAds);
    } else {
      const newAd = {
        ...adData,
        id: Date.now()
      };
      saveAdvertisements([...advertisements, newAd]);
    }
    setShowForm(false);
    setEditingAd(null);
  };

  const getPositionLabel = (position: string) => {
    const labels = {
      hero: 'الصفحة الرئيسية',
      sidebar: 'الشريط الجانبي',
      footer: 'أسفل الصفحة',
      popup: 'نافذة منبثقة'
    };
    return labels[position as keyof typeof labels] || position;
  };

  const getStatusColor = (ad: Advertisement) => {
    if (!ad.isActive) return 'bg-gray-500';
    
    const now = new Date();
    const start = new Date(ad.startDate);
    const end = new Date(ad.endDate);
    
    if (now < start) return 'bg-yellow-500'; // قريباً
    if (now > end) return 'bg-red-500'; // منتهي
    return 'bg-green-500'; // نشط
  };

  const getStatusText = (ad: Advertisement) => {
    if (!ad.isActive) return 'معطل';
    
    const now = new Date();
    const start = new Date(ad.startDate);
    const end = new Date(ad.endDate);
    
    if (now < start) return 'قريباً';
    if (now > end) return 'منتهي';
    return 'نشط';
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-white">إدارة الإعلانات</h1>
        <button
          onClick={handleAddAd}
          className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
        >
          <Plus size={20} />
          <span>إضافة إعلان</span>
        </button>
      </div>

      {showForm && (
        <AdForm
          ad={editingAd}
          onSave={handleSaveAd}
          onCancel={() => {
            setShowForm(false);
            setEditingAd(null);
          }}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {advertisements.map((ad) => (
          <div key={ad.id} className="bg-gray-800/50 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-700">
            <div className="relative h-40">
              <img
                src={ad.image}
                alt={ad.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x200?text=No+Image';
                }}
              />
              <div className="absolute top-2 right-2 flex space-x-2 space-x-reverse">
                <span className={`px-2 py-1 rounded text-xs text-white ${getStatusColor(ad)}`}>
                  {getStatusText(ad)}
                </span>
                <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs">
                  {getPositionLabel(ad.position)}
                </span>
              </div>
            </div>
            
            <div className="p-4">
              <h3 className="text-lg font-bold text-white mb-2">{ad.title}</h3>
              <p className="text-gray-400 mb-3 text-sm">{ad.description}</p>
              
              <div className="text-xs text-gray-500 mb-4">
                <p>من: {new Date(ad.startDate).toLocaleDateString('ar-EG')}</p>
                <p>إلى: {new Date(ad.endDate).toLocaleDateString('ar-EG')}</p>
              </div>
              
              <div className="flex justify-between items-center">
                <button
                  onClick={() => handleToggleActive(ad.id)}
                  className={`flex items-center space-x-1 space-x-reverse px-3 py-1 rounded text-sm transition-colors ${
                    ad.isActive 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }`}
                >
                  {ad.isActive ? <Eye size={14} /> : <EyeOff size={14} />}
                  <span>{ad.isActive ? 'نشط' : 'معطل'}</span>
                </button>
                
                <div className="flex space-x-2 space-x-reverse">
                  <button
                    onClick={() => handleEditAd(ad)}
                    className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteAd(ad.id)}
                    className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {advertisements.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Megaphone size={48} className="mx-auto mb-4" />
            <p>لا توجد إعلانات حالياً</p>
          </div>
        </div>
      )}
    </div>
  );
}

interface AdFormProps {
  ad: Advertisement | null;
  onSave: (ad: Omit<Advertisement, 'id'>) => void;
  onCancel: () => void;
}

function AdForm({ ad, onSave, onCancel }: AdFormProps) {
  const [formData, setFormData] = useState({
    title: ad?.title || '',
    description: ad?.description || '',
    image: ad?.image || '',
    link: ad?.link || '',
    isActive: ad?.isActive ?? true,
    position: ad?.position || 'hero' as const,
    startDate: ad?.startDate || new Date().toISOString().split('T')[0],
    endDate: ad?.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.title && formData.description) {
      onSave(formData);
    }
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white">
          {ad ? 'تعديل الإعلان' : 'إضافة إعلان جديد'}
        </h2>
        <button
          onClick={onCancel}
          className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-white font-medium mb-2">عنوان الإعلان</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            required
          />
        </div>

        <div>
          <label className="block text-white font-medium mb-2">الوصف</label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500 h-24"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-white font-medium mb-2">رابط الصورة</label>
            <input
              type="url"
              value={formData.image}
              onChange={(e) => setFormData({ ...formData, image: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">رابط الإعلان</label>
            <input
              type="text"
              value={formData.link}
              onChange={(e) => setFormData({ ...formData, link: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
              placeholder="#section أو https://example.com"
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">موقع الإعلان</label>
            <select
              value={formData.position}
              onChange={(e) => setFormData({ ...formData, position: e.target.value as any })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            >
              <option value="hero">الصفحة الرئيسية</option>
              <option value="sidebar">الشريط الجانبي</option>
              <option value="footer">أسفل الصفحة</option>
              <option value="popup">نافذة منبثقة</option>
            </select>
          </div>

          <div>
            <label className="flex items-center space-x-2 space-x-reverse text-white">
              <input
                type="checkbox"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="rounded"
              />
              <span>إعلان نشط</span>
            </label>
          </div>

          <div>
            <label className="block text-white font-medium mb-2">تاريخ البداية</label>
            <input
              type="date"
              value={formData.startDate}
              onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">تاريخ النهاية</label>
            <input
              type="date"
              value={formData.endDate}
              onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 space-x-reverse">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
          >
            إلغاء
          </button>
          <button
            type="submit"
            className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
          >
            <Save size={16} />
            <span>حفظ</span>
          </button>
        </div>
      </form>
    </div>
  );
}
