import React from 'react';
import { CheckIcon } from 'lucide-react';
export function Pricing() {
  const plans = [{
    name: 'Cards Design',
    price: '0',
    features: ['Soon', '', '', '']
  }, {
    name: 'Posters Design',
    price: '0',
    features: ['Soon', '', '', '', '']
  }, {
    name: 'Menu Design',
    price: '0',
    features: ['Soon', '', '', '', '', '']
  }];
  return <section id="pricing" className="py-20 bg-black w-full relative overflow-hidden">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-purple-900/20 via-black to-black"></div>
      <div className="container relative mx-auto px-4 md:px-6 z-10">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white [text-shadow:_0_0_10px_rgb(168_85_247_/_0.5)]">
              Design Packages
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-purple-500 via-fuchsia-500 to-pink-500 mx-auto mb-6 shadow-[0_0_15px_rgb(168_85_247_/_0.5)]"></div>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {plans.map((plan, index) => <div key={index} className="group relative bg-black/50 backdrop-blur-sm rounded-lg p-8 border border-purple-500/20 transition-all duration-300 hover:border-purple-500/50 hover:shadow-[0_0_30px_rgba(168,85,247,0.3)] flex flex-col">
                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-purple-400 transition-colors [text-shadow:_0_0_10px_rgb(168_85_247_/_0.3)]">
                  {plan.name}
                </h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-white [text-shadow:_0_0_15px_rgb(168_85_247_/_0.5)]">
                    {plan.price} EGP
                  </span>
                  <span className="text-gray-400">/project</span>
                </div>
                <ul className="space-y-4 mb-8 flex-grow">
                  {plan.features.map((feature, idx) => 
                    feature ? (
                      <li key={idx} className="flex items-center text-gray-300">
                        <CheckIcon size={16} className="text-purple-400 mr-2 group-hover:text-pink-400 transition-colors group-hover:animate-pulse" />
                        {feature}
                      </li>
                    ) : <li key={idx} className="text-gray-300 ml-6">&nbsp;</li>
                  )}
                </ul>
                <button onClick={() => window.open('https://www.facebook.com/profile.php?id=61576318977504', '_blank')} className="w-full py-3 px-6 text-center text-white bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg transition-all duration-300 hover:shadow-[0_0_20px_rgba(168,85,247,0.5)] hover:from-purple-600 hover:to-pink-600 transform hover:scale-105">
                  Get Started
                </button>
              </div>)}
          </div>
        </div>
      </div>
    </section>;
}