import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Image, Save, X } from 'lucide-react';

interface Project {
  id: number;
  title: string;
  category: string;
  image: string;
  description: string;
}

export function ProjectsManager() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = () => {
    const savedProjects = localStorage.getItem('projects');
    if (savedProjects) {
      setProjects(JSON.parse(savedProjects));
    }
  };

  const saveProjects = (updatedProjects: Project[]) => {
    localStorage.setItem('projects', JSON.stringify(updatedProjects));
    setProjects(updatedProjects);
  };

  const handleAddProject = () => {
    setEditingProject(null);
    setShowForm(true);
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setShowForm(true);
  };

  const handleDeleteProject = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
      const updatedProjects = projects.filter(p => p.id !== id);
      saveProjects(updatedProjects);
    }
  };

  const handleSaveProject = (projectData: Omit<Project, 'id'>) => {
    if (editingProject) {
      // تحديث مشروع موجود
      const updatedProjects = projects.map(p => 
        p.id === editingProject.id 
          ? { ...projectData, id: editingProject.id }
          : p
      );
      saveProjects(updatedProjects);
    } else {
      // إضافة مشروع جديد
      const newProject = {
        ...projectData,
        id: Date.now()
      };
      saveProjects([...projects, newProject]);
    }
    setShowForm(false);
    setEditingProject(null);
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
        <h1 className="text-2xl md:text-3xl font-bold text-white">إدارة المشاريع</h1>
        <button
          onClick={handleAddProject}
          className="flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors text-sm md:text-base"
        >
          <Plus size={18} className="md:w-5 md:h-5" />
          <span>إضافة مشروع</span>
        </button>
      </div>

      {showForm && (
        <ProjectForm
          project={editingProject}
          onSave={handleSaveProject}
          onCancel={() => {
            setShowForm(false);
            setEditingProject(null);
          }}
        />
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {projects.map((project) => (
          <div key={project.id} className="bg-gray-800/50 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-700 hover:border-purple-500/50 transition-all duration-300">
            <div className="relative h-40 md:h-48">
              <img
                src={project.image}
                alt={project.title}
                className="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300"
                onClick={() => window.open(project.image, '_blank')}
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x300?text=No+Image';
                }}
              />
              <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                <div className="opacity-0 hover:opacity-100 transition-opacity duration-300 bg-white/20 backdrop-blur-sm rounded-full p-2">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
            <div className="p-3 md:p-4">
              <h3 className="text-base md:text-lg font-bold text-white mb-2 line-clamp-1">{project.title}</h3>
              <p className="text-gray-400 mb-2 text-sm line-clamp-2">{project.description}</p>
              <span className="text-purple-400 text-xs md:text-sm">{project.category}</span>

              <div className="flex justify-end space-x-2 space-x-reverse mt-3 md:mt-4">
                <button
                  onClick={() => handleEditProject(project)}
                  className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                  title="تعديل"
                >
                  <Edit size={14} className="md:w-4 md:h-4" />
                </button>
                <button
                  onClick={() => handleDeleteProject(project.id)}
                  className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                  title="حذف"
                >
                  <Trash2 size={14} className="md:w-4 md:h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {projects.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Image size={48} className="mx-auto mb-4" />
            <p>لا توجد مشاريع حالياً</p>
          </div>
        </div>
      )}
    </div>
  );
}

interface ProjectFormProps {
  project: Project | null;
  onSave: (project: Omit<Project, 'id'>) => void;
  onCancel: () => void;
}

function ProjectForm({ project, onSave, onCancel }: ProjectFormProps) {
  const [formData, setFormData] = useState({
    title: project?.title || '',
    category: project?.category || '',
    image: project?.image || '',
    description: project?.description || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.title && formData.category && formData.description) {
      onSave(formData);
    }
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 md:p-6 border border-gray-700">
      <div className="flex justify-between items-center mb-4 md:mb-6">
        <h2 className="text-lg md:text-xl font-bold text-white">
          {project ? 'تعديل المشروع' : 'إضافة مشروع جديد'}
        </h2>
        <button
          onClick={onCancel}
          className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
        >
          <X size={18} className="md:w-5 md:h-5" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-white font-medium mb-2 text-sm md:text-base">عنوان المشروع</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className="w-full px-3 md:px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500 text-sm md:text-base"
            required
          />
        </div>

        <div>
          <label className="block text-white font-medium mb-2 text-sm md:text-base">الفئة</label>
          <input
            type="text"
            value={formData.category}
            onChange={(e) => setFormData({ ...formData, category: e.target.value })}
            className="w-full px-3 md:px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500 text-sm md:text-base"
            required
          />
        </div>

        <div>
          <label className="block text-white font-medium mb-2 text-sm md:text-base">رابط الصورة</label>
          <input
            type="url"
            value={formData.image}
            onChange={(e) => setFormData({ ...formData, image: e.target.value })}
            className="w-full px-3 md:px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500 text-sm md:text-base"
            placeholder="https://example.com/image.jpg"
          />
        </div>

        <div>
          <label className="block text-white font-medium mb-2 text-sm md:text-base">الوصف</label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            className="w-full px-3 md:px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500 h-20 md:h-24 text-sm md:text-base"
            required
          />
        </div>

        <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 sm:space-x-reverse">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors text-sm md:text-base"
          >
            إلغاء
          </button>
          <button
            type="submit"
            className="flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors text-sm md:text-base"
          >
            <Save size={14} className="md:w-4 md:h-4" />
            <span>حفظ</span>
          </button>
        </div>
      </form>
    </div>
  );
}
