import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, DollarSign, Save, X } from 'lucide-react';

interface PricingPlan {
  id: number;
  name: string;
  price: string;
  features: string[];
}

export function PricingManager() {
  const [pricingPlans, setPricingPlans] = useState<PricingPlan[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingPlan, setEditingPlan] = useState<PricingPlan | null>(null);

  useEffect(() => {
    loadPricingPlans();
  }, []);

  const loadPricingPlans = () => {
    const savedPlans = localStorage.getItem('pricingPlans');
    if (savedPlans) {
      setPricingPlans(JSON.parse(savedPlans));
    } else {
      // تحميل البيانات الافتراضية
      const defaultPlans = [
        {
          id: 1,
          name: 'Cards Design',
          price: '0',
          features: ['Soon', '', '', '']
        },
        {
          id: 2,
          name: 'Posters Design',
          price: '0',
          features: ['Soon', '', '', '', '']
        },
        {
          id: 3,
          name: 'Menu Design',
          price: '0',
          features: ['Soon', '', '', '', '', '']
        }
      ];
      setPricingPlans(defaultPlans);
      localStorage.setItem('pricingPlans', JSON.stringify(defaultPlans));
    }
  };

  const savePricingPlans = (updatedPlans: PricingPlan[]) => {
    localStorage.setItem('pricingPlans', JSON.stringify(updatedPlans));
    setPricingPlans(updatedPlans);
  };

  const handleAddPlan = () => {
    setEditingPlan(null);
    setShowForm(true);
  };

  const handleEditPlan = (plan: PricingPlan) => {
    setEditingPlan(plan);
    setShowForm(true);
  };

  const handleDeletePlan = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه الباقة؟')) {
      const updatedPlans = pricingPlans.filter(p => p.id !== id);
      savePricingPlans(updatedPlans);
    }
  };

  const handleSavePlan = (planData: Omit<PricingPlan, 'id'>) => {
    if (editingPlan) {
      const updatedPlans = pricingPlans.map(p => 
        p.id === editingPlan.id 
          ? { ...planData, id: editingPlan.id }
          : p
      );
      savePricingPlans(updatedPlans);
    } else {
      const newPlan = {
        ...planData,
        id: Date.now()
      };
      savePricingPlans([...pricingPlans, newPlan]);
    }
    setShowForm(false);
    setEditingPlan(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-white">إدارة الأسعار</h1>
        <button
          onClick={handleAddPlan}
          className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
        >
          <Plus size={20} />
          <span>إضافة باقة</span>
        </button>
      </div>

      {showForm && (
        <PricingPlanForm
          plan={editingPlan}
          onSave={handleSavePlan}
          onCancel={() => {
            setShowForm(false);
            setEditingPlan(null);
          }}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {pricingPlans.map((plan) => (
          <div key={plan.id} className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
              <div className="text-3xl font-bold text-purple-400 mb-4">
                {plan.price} EGP
                <span className="text-sm text-gray-400 font-normal">/project</span>
              </div>
            </div>

            <div className="space-y-3 mb-6">
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  {feature ? (
                    <>
                      <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                      <span className="text-gray-300">{feature}</span>
                    </>
                  ) : (
                    <span className="text-gray-500 mr-5">-</span>
                  )}
                </div>
              ))}
            </div>

            <div className="flex justify-end space-x-2 space-x-reverse">
              <button
                onClick={() => handleEditPlan(plan)}
                className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => handleDeletePlan(plan.id)}
                className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>

      {pricingPlans.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <DollarSign size={48} className="mx-auto mb-4" />
            <p>لا توجد باقات أسعار حالياً</p>
          </div>
        </div>
      )}
    </div>
  );
}

interface PricingPlanFormProps {
  plan: PricingPlan | null;
  onSave: (plan: Omit<PricingPlan, 'id'>) => void;
  onCancel: () => void;
}

function PricingPlanForm({ plan, onSave, onCancel }: PricingPlanFormProps) {
  const [formData, setFormData] = useState({
    name: plan?.name || '',
    price: plan?.price || '',
    features: plan?.features || ['']
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name && formData.price) {
      onSave(formData);
    }
  };

  const addFeature = () => {
    setFormData({
      ...formData,
      features: [...formData.features, '']
    });
  };

  const updateFeature = (index: number, value: string) => {
    const updatedFeatures = [...formData.features];
    updatedFeatures[index] = value;
    setFormData({ ...formData, features: updatedFeatures });
  };

  const removeFeature = (index: number) => {
    const updatedFeatures = formData.features.filter((_, i) => i !== index);
    setFormData({ ...formData, features: updatedFeatures });
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white">
          {plan ? 'تعديل الباقة' : 'إضافة باقة جديدة'}
        </h2>
        <button
          onClick={onCancel}
          className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-white font-medium mb-2">اسم الباقة</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
              required
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">السعر (EGP)</label>
            <input
              type="text"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: e.target.value })}
              className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-white font-medium mb-2">المميزات</label>
          <div className="space-y-2">
            {formData.features.map((feature, index) => (
              <div key={index} className="flex space-x-2 space-x-reverse">
                <input
                  type="text"
                  value={feature}
                  onChange={(e) => updateFeature(index, e.target.value)}
                  placeholder="اكتب المميزة أو اتركها فارغة"
                  className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
                />
                <button
                  type="button"
                  onClick={() => removeFeature(index)}
                  className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                  disabled={formData.features.length === 1}
                >
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={addFeature}
            className="mt-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
          >
            إضافة مميزة
          </button>
        </div>

        <div className="flex justify-end space-x-3 space-x-reverse">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
          >
            إلغاء
          </button>
          <button
            type="submit"
            className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
          >
            <Save size={16} />
            <span>حفظ</span>
          </button>
        </div>
      </form>
    </div>
  );
}
