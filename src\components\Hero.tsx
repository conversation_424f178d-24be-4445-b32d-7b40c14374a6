import React, { useEffect, useRef, useCallback } from 'react';
import { ArrowDownIcon, GithubIcon, LinkedinIcon, TwitterIcon } from 'lucide-react';
import * as THREE from 'three';

interface HeroProps {
  setShowAllWorks: (show: boolean) => void;
  socialLinks?: {
    github?: string;
    linkedin?: string;
    twitter?: string;
  };
}

export const Hero: React.FC<HeroProps> = React.memo(({ setShowAllWorks, socialLinks = {} }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const spheresRef = useRef<THREE.Mesh[]>([]);
  const animationFrameRef = useRef<number | null>(null);

  const animate = useCallback(() => {
    if (!sceneRef.current || !cameraRef.current || !rendererRef.current) return;

    spheresRef.current.forEach((sphere, i) => {
      sphere.rotation.x += 0.001;
      sphere.rotation.y += 0.002;
      const time = Date.now() * 0.001;
      sphere.position.y += Math.sin(time + i) * 0.01;
      sphere.position.x += Math.cos(time + i) * 0.01;
      const baseScale = sphere.userData.baseScale || 1;
      const scale = 1 + Math.sin(time * 0.5 + i) * 0.1;
      sphere.scale.setScalar(baseScale * scale);
    });

    rendererRef.current.render(sceneRef.current, cameraRef.current);
    animationFrameRef.current = requestAnimationFrame(animate);
  }, []);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    sceneRef.current = new THREE.Scene();
    cameraRef.current = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    rendererRef.current = new THREE.WebGLRenderer({
      alpha: true,
      antialias: true,
    });
    const renderer = rendererRef.current;
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    containerRef.current.appendChild(renderer.domElement);

    // Create floating spheres
    for (let i = 0; i < 8; i++) {
      const geometry = new THREE.SphereGeometry(1.5, 32, 32);
      const material = new THREE.MeshPhongMaterial({
        color: new THREE.Color(i % 2 ? 0x9333ea : 0xec4899),
        transparent: true,
        opacity: 0.15,
        shininess: 150,
      });
      const sphere = new THREE.Mesh(geometry, material);
      sphere.position.set(
        Math.random() * 30 - 15,
        Math.random() * 30 - 15,
        Math.random() * 20 - 25
      );
      const baseScale = Math.random() * 2 + 0.5;
      sphere.scale.setScalar(baseScale);
      sphere.userData.baseScale = baseScale;
      sceneRef.current.add(sphere);
      spheresRef.current.push(sphere);
    }

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    sceneRef.current.add(ambientLight);
    const pointLight1 = new THREE.PointLight(0x9333ea, 2);
    pointLight1.position.set(10, 10, 10);
    sceneRef.current.add(pointLight1);
    const pointLight2 = new THREE.PointLight(0xec4899, 2);
    pointLight2.position.set(-10, -10, 10);
    sceneRef.current.add(pointLight2);
    cameraRef.current.position.z = 20;

    // Start animation
    animate();

    // Handle resize
    const handleResize = () => {
      if (!cameraRef.current || !rendererRef.current) return;
      cameraRef.current.aspect = window.innerWidth / window.innerHeight;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(window.innerWidth, window.innerHeight);
    };
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (rendererRef.current) {
        spheresRef.current.forEach((sphere) => {
          const geometry = sphere.geometry as THREE.BufferGeometry;
          const material = sphere.material as THREE.Material;
          geometry.dispose();
          material.dispose();
          sceneRef.current?.remove(sphere);
        });
        spheresRef.current = [];
        if (containerRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
        }
        rendererRef.current.dispose();
      }
    };
  }, [animate]);

  const socialIcons = [
    { Icon: GithubIcon, href: socialLinks.github, label: 'GitHub' },
    { Icon: LinkedinIcon, href: socialLinks.linkedin, label: 'LinkedIn' },
    { Icon: TwitterIcon, href: socialLinks.twitter, label: 'Twitter' },
  ].filter((item) => item.href);

  return (
    <section id="home" className="relative w-full min-h-screen overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-purple-900/20 to-pink-900/20"></div>
      <div ref={containerRef} className="absolute inset-0" />
      <div className="container relative mx-auto px-4 md:px-6 z-10 h-screen flex items-center">
        <div className="max-w-4xl">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
            AR Team{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 animate-pulse">
              Graphic
            </span>{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-purple-400 animate-pulse">
              Designers
            </span>
          </h1>
          <p className="text-lg md:text-xl text-gray-300 mb-8 max-w-2xl">
            I create beautiful, functional, and user-friendly digital experiences with modern technologies and creative solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 mb-12">
            <button
              onClick={() => window.open('https://www.facebook.com/dark50014', '_blank')}
              className="px-8 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-medium transition-all duration-300 hover:shadow-[0_0_20px_rgba(236,72,153,0.5)] hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              View My Work
            </button>
            <a
              href="#contact"
              className="px-8 py-3 border border-purple-500/20 rounded-full text-purple-400 font-medium transition-all duration-300 hover:border-purple-500/50 hover:shadow-[0_0_20px_rgba(168,85,247,0.3)] hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              Contact Me
            </a>
          </div>
          <div className="flex space-x-4">
            {socialIcons.map(({ Icon, href, label }, index) => (
              <a
                key={index}
                href={href}
                aria-label={`Visit my ${label} profile`}
                target="_blank"
                rel="noopener noreferrer"
                className="group relative p-2 rounded-full transition-all duration-300 bg-purple-900/20 hover:bg-purple-900/40 hover:shadow-[0_0_15px_rgb(168_85_247)] transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <div className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur"></div>
                <Icon
                  size={24}
                  className="relative text-purple-400 group-hover:text-pink-400 transition-colors group-hover:scale-110 duration-300 [filter:drop-shadow(0_0_8px_rgb(168_85_247))]"
                />
              </a>
            ))}
          </div>
        </div>
      </div>
      <a
        href="#about"
        onClick={(e) => {
          e.preventDefault();
          document.querySelector('#about')?.scrollIntoView({ behavior: 'smooth' });
        }}
        aria-label="Scroll to About section"
        className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce cursor-pointer group p-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
      >
        <div className="absolute inset-0 bg-white/10 rounded-full backdrop-blur-sm group-hover:bg-white/20 transition-colors"></div>
        <ArrowDownIcon size={24} className="relative text-white group-hover:scale-110 transition-transform" />
      </a>
    </section>
  );
});

export default Hero;