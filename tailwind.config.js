export default {
  content: [
  './index.html',
  './src/**/*.{js,ts,jsx,tsx}'
],
  theme: {
    extend: {
      animation: {
        blob: "blob 7s infinite",
        shine: "shine 1.5s infinite",
        "spin-slow": "spin 8s linear infinite",
        pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },
      keyframes: {
        blob: {
          "0%": {
            transform: "translate(0px, 0px) scale(1)",
          },
          "33%": {
            transform: "translate(30px, -50px) scale(1.1)",
          },
          "66%": {
            transform: "translate(-20px, 20px) scale(0.9)",
          },
          "100%": {
            transform: "translate(0px, 0px) scale(1)",
          },
        },
        shine: {
          "100%": {
            transform: "translateX(100%)",
          },
        },
        pulse: {
          '0%, 100%': {
            opacity: '0.2',
          },
          '50%': {
            opacity: '0.4',
          },
        },
      },
    },
  },
  plugins: [],
};