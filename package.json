{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "vite preview"}, "dependencies": {"@types/three": "^0.176.0", "lucide-react": "latest", "react": "^18.3.1", "react-dom": "^18.3.1", "three": "^0.176.0"}, "devDependencies": {"@types/node": "^20.11.18", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "latest", "tailwindcss": "3.4.17", "typescript": "^5.8.2", "vite": "^5.2.0"}}