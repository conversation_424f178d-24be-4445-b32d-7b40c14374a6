import React, { useEffect, useState } from 'react';
import { MenuIcon, XIcon } from 'lucide-react';
export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const menuItems = ['Home', 'About', 'Team', 'Pricing', 'Contact'];
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
      const sections = menuItems.map(item => document.getElementById(item.toLowerCase()));
      const scrollPosition = window.scrollY + 100;
      sections.forEach(section => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.clientHeight;
          if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
            setActiveSection(section.id);
          }
        }
      });
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  return <header className={`fixed top-0 w-full z-50 transition-all duration-300 ${scrolled ? 'bg-black/80 backdrop-blur-sm shadow-[0_0_15px_rgba(0,0,0,0.5)]' : 'bg-transparent'} py-4`}>
      <div className="container mx-auto px-4 md:px-6 flex justify-between items-center">
        <div className="group relative">
          <div className="text-2xl font-bold text-white transition-all duration-300 group-hover:text-purple-300 [text-shadow:_0_0_10px_rgb(168_85_247_/_0.5),_0_0_20px_rgb(168_85_247_/_0.3),_0_0_30px_rgb(168_85_247_/_0.2)]">
            AR Designers
          </div>
          <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-purple-500 via-fuchsia-500 to-pink-500 transform scale-x-0 transition-transform origin-left group-hover:scale-x-100 group-hover:shadow-[0_0_10px_rgb(168_85_247_/_0.5)]"></span>
        </div>
        <nav className="hidden md:flex space-x-8">
          {menuItems.map(item => <a key={item} href={`#${item.toLowerCase()}`} className={`font-medium text-gray-300 hover:text-purple-400 transition-colors relative group [text-shadow:_0_0_10px_rgb(168_85_247_/_0.3)] ${activeSection === item.toLowerCase() ? 'text-purple-400' : ''}`}>
              {item}
              <span className={`absolute -bottom-1 left-0 w-full h-0.5 bg-purple-400 transform scale-x-0 transition-transform origin-left group-hover:scale-x-100 ${activeSection === item.toLowerCase() ? 'scale-x-100' : ''}`}></span>
            </a>)}
        </nav>
        <button className="md:hidden text-white" onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? <XIcon size={24} /> : <MenuIcon size={24} />}
        </button>
      </div>
      {isOpen && <div className="md:hidden bg-black/90 backdrop-blur-sm w-full shadow-lg">
          <div className="container mx-auto px-4 py-4 flex flex-col space-y-4">
            {menuItems.map(item => <a key={item} href={`#${item.toLowerCase()}`} className={`font-medium text-gray-300 hover:text-purple-400 transition-colors py-2 relative group ${activeSection === item.toLowerCase() ? 'text-purple-400' : ''}`} onClick={() => setIsOpen(false)}>
                {item}
                <span className={`absolute -bottom-1 left-0 w-full h-0.5 bg-purple-400 transform scale-x-0 transition-transform origin-left group-hover:scale-x-100 ${activeSection === item.toLowerCase() ? 'scale-x-100' : ''}`}></span>
              </a>)}
          </div>
        </div>}
    </header>;
}